#!/usr/bin/env python3
"""
页面结构抓取器使用示例
"""

import asyncio
import json
from page_structure_crawler import PageStructureCrawler


async def example_usage():
    """示例用法"""

    # 要抓取的网站列表
    test_urls = [
        "https://example.com",
        "https://httpbin.org/html",
        "https://news.ycombinator.com",
    ]

    # 创建抓取器实例
    crawler = PageStructureCrawler(headless=True, timeout=30000)

    for url in test_urls:
        print(f"\n{'='*60}")
        print(f"正在抓取: {url}")
        print('='*60)

        try:
            # 抓取页面结构
            page_structure = await crawler.crawl_page_structure(url)

            # 显示基本信息
            print(f"📄 标题: {page_structure.title}")
            print(f"📝 描述: {page_structure.description}")
            print(f"📊 标题数量: {len(page_structure.headings)}")
            print(f"🔗 链接数量: {len(page_structure.links)}")
            print(f"🖼️ 图片数量: {len(page_structure.images)}")
            print(f"📄 内容长度: {len(page_structure.text_content)} 字符")

            # 显示前几个标题
            if page_structure.headings:
                print(f"\n📋 前5个标题:")
                for i, heading in enumerate(page_structure.headings[:5], 1):
                    print(f"  {i}. [{heading['level']}] {heading['text']}")

            # 显示前几个链接
            if page_structure.links:
                print(f"\n🔗 前5个链接:")
                for i, link in enumerate(page_structure.links[:5], 1):
                    print(f"  {i}. {link['text'][:50]}... -> {link['href']}")

            print("✅ 抓取成功!")

        except Exception as e:
            print(f"❌ 抓取失败: {str(e)}")


async def custom_extraction_example():
    """自定义提取示例"""
    print(f"\n{'='*60}")
    print("自定义提取示例 - 抓取特定内容")
    print('='*60)

    url = "https://httpbin.org/html"

    try:
        crawler = PageStructureCrawler(headless=True)
        page_structure = await crawler.crawl_page_structure(url)

        # 将结果保存为JSON
        result_data = page_structure.model_dump()

        # 美化输出
        print("📄 页面结构 (JSON格式):")
        print(json.dumps(result_data, ensure_ascii=False, indent=2))

        # 保存到文件
        with open('example_result.json', 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)

        print("\n💾 结果已保存到 example_result.json")

    except Exception as e:
        print(f"❌ 抓取失败: {str(e)}")


async def main():
    """主函数"""
    print("🚀 页面结构抓取器示例")
    print("基于 crawl4ai 库")

    # 运行基本示例
    await example_usage()

    # 运行自定义提取示例
    await custom_extraction_example()

    print(f"\n{'='*60}")
    print("✨ 所有示例运行完成!")
    print("💡 提示: 你可以直接运行 'python page_structure_crawler.py <URL>' 来抓取特定网页")
    print('='*60)


if __name__ == "__main__":
    asyncio.run(main())
