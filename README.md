# 页面结构抓取器

基于 [crawl4ai](https://github.com/unclecode/crawl4ai) 库开发的网页结构抓取工具，可以传入网址并抓取页面的结构信息。

## 功能特性

- 🚀 **快速抓取**: 基于 crawl4ai 的高性能异步抓取
- 📄 **结构化提取**: 自动提取页面标题、链接、图片、文本内容
- 🎯 **智能解析**: 使用 CSS 选择器精确提取页面元素
- 💾 **多格式输出**: 支持控制台显示和 JSON 文件保存
- 🔧 **可配置**: 支持自定义浏览器配置和超时设置

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install crawl4ai asyncio pydantic beautifulsoup4 lxml
```

## 使用方法

### 1. 命令行使用

```bash
python page_structure_crawler.py <URL>
```

示例：
```bash
python page_structure_crawler.py https://example.com
python page_structure_crawler.py https://news.ycombinator.com
```

### 2. 作为模块使用

```python
import asyncio
from page_structure_crawler import PageStructureCrawler

async def main():
    crawler = PageStructureCrawler(headless=True, timeout=30000)
    page_structure = await crawler.crawl_page_structure("https://example.com")

    print(f"页面标题: {page_structure.title}")
    print(f"链接数量: {len(page_structure.links)}")
    print(f"图片数量: {len(page_structure.images)}")

asyncio.run(main())
```

### 3. 运行示例

```bash
python example.py
```

### 4. 批量抓取

```bash
python batch_crawler.py
```

批量抓取器支持同时抓取多个网址，并生成详细的抓取报告。

## 输出文件夹

所有抓取结果都会自动保存在 `crawl_results/` 文件夹中，包括：

- **单个页面抓取**: `page_structure_<域名>.json`
- **本地HTML文件**: `page_structure_<文件名>.json`
- **批量抓取结果**: `batch_<域名>_<时间戳>.json`
- **批量抓取报告**: `batch_report_<时间戳>.json`

这样可以更好地组织和管理抓取的数据，避免文件混乱。

## 输出结果

程序会提取以下页面结构信息：

- **基本信息**: 页面标题、URL、描述
- **标题结构**: 所有 h1-h6 标题及其层级
- **链接信息**: 页面中的所有链接（文本和URL）
- **图片信息**: 页面中的所有图片（源地址、alt文本等）
- **文本内容**: 页面的 Markdown 格式文本
- **统计信息**: 内容长度、元素数量等

### 控制台输出示例

```
================================================================================
页面结构抓取结果
================================================================================

📄 页面信息:
  标题: Example Domain
  URL: https://example.com
  描述: Example Domain description

📊 统计信息:
  status_code: None
  content_length: 1256
  markdown_length: 298
  headings_count: 1
  links_count: 1
  images_count: 0

📋 页面标题 (1个):
  1. [h1] Example Domain

🔗 页面链接 (1个):
  1. More information... -> https://www.iana.org/domains/example

💾 详细结果已保存到: crawl_results/page_structure_example_com.json
```

### JSON 输出示例

```json
{
  "url": "https://example.com",
  "title": "Example Domain",
  "description": "Example Domain description",
  "headings": [
    {
      "level": "h1",
      "text": "Example Domain"
    }
  ],
  "links": [
    {
      "text": "More information...",
      "href": "https://www.iana.org/domains/example",
      "title": ""
    }
  ],
  "images": [],
  "text_content": "# Example Domain\n\nThis domain is for use in illustrative examples...",
  "meta_info": {
    "status_code": null,
    "content_length": 1256,
    "markdown_length": 298,
    "headings_count": 1,
    "links_count": 1,
    "images_count": 0
  }
}
```

## 配置选项

### PageStructureCrawler 参数

- `headless` (bool): 是否使用无头浏览器，默认 True
- `timeout` (int): 页面加载超时时间（毫秒），默认 30000

### 自定义配置示例

```python
# 使用有头浏览器，延长超时时间
crawler = PageStructureCrawler(headless=False, timeout=60000)

# 抓取需要 JavaScript 渲染的页面
page_structure = await crawler.crawl_page_structure("https://spa-website.com")
```

## 项目结构

```
.
├── page_structure_crawler.py  # 主程序文件
├── example.py                 # 使用示例
├── batch_crawler.py           # 批量抓取器
├── requirements.txt           # 依赖包列表
├── README.md                 # 说明文档
├── test.html                 # 测试HTML文件
└── crawl_results/            # 抓取结果存储文件夹
    ├── page_structure_example_com.json      # 单个页面抓取结果
    ├── page_structure_test.json             # 本地HTML文件抓取结果
    ├── example_result.json                  # 示例程序结果
    ├── batch_example_com_20250526_112222.json    # 批量抓取结果
    ├── batch_httpbin_org_20250526_112228.json    # 批量抓取结果
    ├── batch_httpbin_org_20250526_112232.json    # 批量抓取结果
    └── batch_report_20250526_112232.json         # 批量抓取报告
```

## 依赖说明

- **crawl4ai**: 核心抓取库，提供异步网页抓取功能
- **pydantic**: 数据验证和序列化
- **beautifulsoup4**: HTML 解析
- **lxml**: XML/HTML 解析器
- **asyncio**: 异步编程支持

## 注意事项

1. **首次运行**: crawl4ai 首次运行时会自动下载 Playwright 浏览器，可能需要一些时间
2. **网络环境**: 确保网络连接正常，某些网站可能需要代理访问
3. **反爬虫**: 部分网站有反爬虫机制，可能需要调整请求头或使用代理
4. **资源消耗**: 抓取大型网站时会消耗较多内存和CPU资源

## 故障排除

### 常见问题

1. **安装失败**: 确保 Python 版本 >= 3.7，并使用最新的 pip
2. **浏览器下载失败**: 可能需要设置代理或手动安装 Playwright
3. **抓取超时**: 增加 timeout 参数值或检查网络连接
4. **权限错误**: 在某些系统上可能需要管理员权限

### 调试模式

设置 `headless=False` 可以看到浏览器操作过程：

```python
crawler = PageStructureCrawler(headless=False)
```

## 许可证

本项目基于 MIT 许可证开源。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 相关链接

- [crawl4ai 官方文档](https://docs.crawl4ai.com/)
- [crawl4ai GitHub 仓库](https://github.com/unclecode/crawl4ai)
