#!/usr/bin/env python3
"""
批量页面结构抓取器
支持批量抓取多个网址的页面结构
"""

import asyncio
import json
import os
from datetime import datetime
from page_structure_crawler import PageStructureCrawler


async def batch_crawl(urls, output_dir="crawl_results"):
    """
    批量抓取多个URL的页面结构
    
    Args:
        urls: URL列表
        output_dir: 输出目录
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 创建抓取器
    crawler = PageStructureCrawler(headless=True, timeout=30000)
    
    results = []
    
    print(f"🚀 开始批量抓取 {len(urls)} 个网址...")
    print("="*60)
    
    for i, url in enumerate(urls, 1):
        print(f"\n[{i}/{len(urls)}] 正在抓取: {url}")
        
        try:
            # 抓取页面结构
            page_structure = await crawler.crawl_page_structure(url)
            
            # 生成文件名
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.replace('.', '_').replace(':', '_')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_{domain}_{timestamp}.json"
            
            # 保存到文件
            output_file = os.path.join(output_dir, filename)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(page_structure.model_dump(), f, ensure_ascii=False, indent=2)
            
            # 记录结果
            result = {
                'url': url,
                'status': 'success',
                'title': page_structure.title,
                'headings_count': len(page_structure.headings),
                'links_count': len(page_structure.links),
                'images_count': len(page_structure.images),
                'output_file': filename
            }
            results.append(result)
            
            print(f"  ✅ 成功 - 标题: {page_structure.title}")
            print(f"     📊 标题: {len(page_structure.headings)}, 链接: {len(page_structure.links)}, 图片: {len(page_structure.images)}")
            print(f"     💾 保存到: {output_file}")
            
        except Exception as e:
            print(f"  ❌ 失败 - {str(e)}")
            result = {
                'url': url,
                'status': 'failed',
                'error': str(e),
                'output_file': None
            }
            results.append(result)
    
    # 生成批量抓取报告
    report_file = os.path.join(output_dir, f"batch_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    report = {
        'timestamp': datetime.now().isoformat(),
        'total_urls': len(urls),
        'successful': len([r for r in results if r['status'] == 'success']),
        'failed': len([r for r in results if r['status'] == 'failed']),
        'results': results
    }
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("\n" + "="*60)
    print("📊 批量抓取完成!")
    print(f"总计: {len(urls)} 个URL")
    print(f"成功: {report['successful']} 个")
    print(f"失败: {report['failed']} 个")
    print(f"📄 详细报告: {report_file}")
    
    return results


async def main():
    """主函数"""
    # 示例URL列表
    urls = [
        "https://example.com",
        "https://httpbin.org/html",
        "https://httpbin.org/json",
        # 可以添加更多URL
    ]
    
    print("🔍 批量页面结构抓取器")
    print("基于 crawl4ai 库")
    print(f"准备抓取 {len(urls)} 个网址...")
    
    # 执行批量抓取
    results = await batch_crawl(urls)
    
    # 显示成功的结果摘要
    successful_results = [r for r in results if r['status'] == 'success']
    if successful_results:
        print(f"\n📋 成功抓取的网站摘要:")
        for result in successful_results:
            print(f"  • {result['url']}")
            print(f"    标题: {result.get('title', '无标题')}")
            print(f"    结构: {result['headings_count']}个标题, {result['links_count']}个链接, {result['images_count']}个图片")


if __name__ == "__main__":
    asyncio.run(main())
