#!/usr/bin/env python3
"""
SPA页面结构抓取器
专门针对单页应用（SPA）进行优化的抓取器
"""

import asyncio
import json
import os
import sys
from typing import Dict, List, Any
from urllib.parse import urljoin, urlparse
from page_structure_crawler import PageStructureCrawler, PageStructure


class SPAPageStructureCrawler(PageStructureCrawler):
    """SPA页面结构抓取器类"""

    def __init__(self, headless: bool = True, timeout: int = 60000, wait_for_selector: str = None,
                 wait_for_text: str = None, scroll_to_bottom: bool = True):
        """
        初始化SPA抓取器

        Args:
            headless: 是否使用无头浏览器
            timeout: 页面加载超时时间（毫秒）
            wait_for_selector: 等待特定CSS选择器出现
            wait_for_text: 等待特定文本出现
            scroll_to_bottom: 是否滚动到页面底部（触发懒加载）
        """
        super().__init__(headless=headless, timeout=timeout, spa_mode=True, wait_for_selector=wait_for_selector)
        self.headless = headless
        self.wait_for_text = wait_for_text
        self.scroll_to_bottom = scroll_to_bottom

    async def crawl_spa_page_structure(self, url: str, custom_js: str = None) -> PageStructure:
        """
        抓取SPA页面结构

        Args:
            url: 要抓取的网页URL
            custom_js: 自定义JavaScript代码（用于特殊的SPA交互）

        Returns:
            PageStructure: 页面结构信息
        """
        from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode

        # SPA专用配置
        browser_config = BrowserConfig(
            headless=self.headless,
            java_script_enabled=True,
            extra_args=["--no-sandbox", "--disable-dev-shm-usage"]  # 提高稳定性
        )

        # 构建JavaScript代码来等待SPA加载完成
        js_code_parts = []

        # 基础等待逻辑
        base_wait_js = """
        console.log('开始等待SPA页面加载...');

        // 等待DOM加载完成
        if (document.readyState !== 'complete') {
            await new Promise(resolve => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve);
                }
            });
        }

        console.log('DOM加载完成，等待JavaScript渲染...');

        // 等待一段时间让JavaScript执行
        await new Promise(resolve => setTimeout(resolve, 3000));
        """
        js_code_parts.append(base_wait_js)

        # 等待特定选择器
        if self.wait_for_selector:
            selector_wait_js = f"""
            console.log('等待选择器: {self.wait_for_selector}');
            let attempts = 0;
            while (attempts < 30 && !document.querySelector('{self.wait_for_selector}')) {{
                await new Promise(resolve => setTimeout(resolve, 1000));
                attempts++;
            }}
            if (document.querySelector('{self.wait_for_selector}')) {{
                console.log('选择器找到了!');
            }} else {{
                console.log('选择器未找到，继续执行...');
            }}
            """
            js_code_parts.append(selector_wait_js)

        # 等待特定文本
        if self.wait_for_text:
            text_wait_js = f"""
            console.log('等待文本: {self.wait_for_text}');
            let textAttempts = 0;
            while (textAttempts < 30 && !document.body.innerText.includes('{self.wait_for_text}')) {{
                await new Promise(resolve => setTimeout(resolve, 1000));
                textAttempts++;
            }}
            if (document.body.innerText.includes('{self.wait_for_text}')) {{
                console.log('文本找到了!');
            }} else {{
                console.log('文本未找到，继续执行...');
            }}
            """
            js_code_parts.append(text_wait_js)

        # 滚动到底部（触发懒加载）
        if self.scroll_to_bottom:
            scroll_js = """
            console.log('开始滚动页面...');
            const scrollToBottom = async () => {
                let lastHeight = 0;
                let currentHeight = document.body.scrollHeight;
                let scrollAttempts = 0;

                while (lastHeight !== currentHeight && scrollAttempts < 10) {
                    lastHeight = currentHeight;
                    window.scrollTo(0, currentHeight);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    currentHeight = document.body.scrollHeight;
                    scrollAttempts++;
                }

                // 滚动回顶部
                window.scrollTo(0, 0);
                await new Promise(resolve => setTimeout(resolve, 1000));
                console.log('滚动完成');
            };
            await scrollToBottom();
            """
            js_code_parts.append(scroll_js)

        # 添加自定义JavaScript
        if custom_js:
            js_code_parts.append(custom_js)

        # 最终等待
        final_wait_js = """
        console.log('最终等待...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('SPA页面处理完成!');
        """
        js_code_parts.append(final_wait_js)

        final_js = "\n".join(js_code_parts)

        crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            page_timeout=self.timeout,
            word_count_threshold=1,
            wait_until="networkidle",
            delay_before_return_html=2000,  # 额外等待2秒
            js_code=[final_js]
        )

        async with AsyncWebCrawler(config=browser_config) as crawler:
            print(f"🔄 正在抓取SPA页面: {url}")
            print("⏳ 等待JavaScript渲染完成...")

            # 获取基本页面内容
            result = await crawler.arun(url=url, config=crawler_config)

            if not result.success:
                raise Exception(f"SPA页面抓取失败: {result.error_message}")

            print("✅ JavaScript渲染完成，开始解析页面结构...")

            # 使用父类的解析逻辑
            return await self._parse_page_content(result, url)

    async def _parse_page_content(self, result, url: str) -> PageStructure:
        """解析页面内容（复用父类逻辑）"""
        # 使用 BeautifulSoup 直接解析 HTML
        headings = []
        links = []
        images = []

        if result.html:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(result.html, 'html.parser')

            # 提取标题
            for heading_tag in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                text = heading_tag.get_text().strip()
                if text:
                    headings.append({
                        'level': heading_tag.name,
                        'text': text
                    })

            # 提取链接
            for link_tag in soup.find_all('a', href=True):
                href = link_tag.get('href', '').strip()
                text = link_tag.get_text().strip()
                if href and text:
                    # 转换相对链接为绝对链接
                    if not url.startswith('raw://'):
                        absolute_href = urljoin(url, href)
                    else:
                        absolute_href = href
                    links.append({
                        'text': text,
                        'href': absolute_href,
                        'title': link_tag.get('title', '').strip()
                    })

            # 提取图片
            for img_tag in soup.find_all('img', src=True):
                src = img_tag.get('src', '').strip()
                if src:
                    # 转换相对链接为绝对链接
                    if not url.startswith('raw://'):
                        absolute_src = urljoin(url, src)
                    else:
                        absolute_src = src
                    images.append({
                        'src': absolute_src,
                        'alt': img_tag.get('alt', '').strip(),
                        'title': img_tag.get('title', '').strip(),
                        'width': img_tag.get('width', ''),
                        'height': img_tag.get('height', '')
                    })

        # 提取页面标题和描述
        title = ""
        description = ""

        if result.html:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(result.html, 'html.parser')

            # 提取标题
            title_tag = soup.find('title')
            if title_tag:
                title = title_tag.get_text().strip()

            # 提取描述
            desc_tag = soup.find('meta', attrs={'name': 'description'})
            if desc_tag:
                description = desc_tag.get('content', '').strip()

        # 构建页面结构对象
        page_structure = PageStructure(
            url=url,
            title=title,
            description=description,
            headings=headings,
            links=links,
            images=images,
            text_content=result.markdown.raw_markdown if result.markdown else "",
            meta_info={
                'status_code': getattr(result, 'status_code', None),
                'content_length': len(result.html) if result.html else 0,
                'markdown_length': len(result.markdown.raw_markdown) if result.markdown else 0,
                'headings_count': len(headings),
                'links_count': len(links),
                'images_count': len(images),
                'spa_mode': True
            }
        )

        return page_structure


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python spa_crawler.py <URL> [wait_for_selector] [wait_for_text]")
        print("示例: python spa_crawler.py http://localhost:3000")
        print("示例: python spa_crawler.py http://localhost:3000 '.content' '加载完成'")
        sys.exit(1)

    url = sys.argv[1]
    wait_for_selector = sys.argv[2] if len(sys.argv) > 2 else None
    wait_for_text = sys.argv[3] if len(sys.argv) > 3 else None

    # 验证URL格式
    parsed_url = urlparse(url)
    if not parsed_url.scheme or not parsed_url.netloc:
        print("错误: 请提供有效的URL（包含http://或https://）")
        sys.exit(1)

    print(f"🚀 正在抓取SPA页面结构: {url}")
    if wait_for_selector:
        print(f"⏳ 等待选择器: {wait_for_selector}")
    if wait_for_text:
        print(f"⏳ 等待文本: {wait_for_text}")

    try:
        crawler = SPAPageStructureCrawler(
            headless=True,
            timeout=60000,
            wait_for_selector=wait_for_selector,
            wait_for_text=wait_for_text,
            scroll_to_bottom=True
        )

        page_structure = await crawler.crawl_spa_page_structure(url)

        # 输出结果
        print("\n" + "="*80)
        print("SPA页面结构抓取结果")
        print("="*80)

        print(f"\n📄 页面信息:")
        print(f"  标题: {page_structure.title}")
        print(f"  URL: {page_structure.url}")
        print(f"  描述: {page_structure.description}")

        print(f"\n📊 统计信息:")
        for key, value in page_structure.meta_info.items():
            print(f"  {key}: {value}")

        if page_structure.headings:
            print(f"\n📋 页面标题 ({len(page_structure.headings)}个):")
            for i, heading in enumerate(page_structure.headings[:10], 1):
                print(f"  {i}. [{heading['level']}] {heading['text']}")
            if len(page_structure.headings) > 10:
                print(f"  ... 还有 {len(page_structure.headings) - 10} 个标题")

        if page_structure.links:
            print(f"\n🔗 页面链接 ({len(page_structure.links)}个):")
            for i, link in enumerate(page_structure.links[:10], 1):
                print(f"  {i}. {link['text']} -> {link['href']}")
            if len(page_structure.links) > 10:
                print(f"  ... 还有 {len(page_structure.links) - 10} 个链接")

        if page_structure.images:
            print(f"\n🖼️ 页面图片 ({len(page_structure.images)}个):")
            for i, img in enumerate(page_structure.images[:10], 1):
                print(f"  {i}. {img['alt'] or '无描述'} -> {img['src']}")
            if len(page_structure.images) > 10:
                print(f"  ... 还有 {len(page_structure.images) - 10} 个图片")

        # 创建输出文件夹
        output_dir = "crawl_results"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 生成文件名
        domain = parsed_url.netloc.replace('.', '_').replace(':', '_')
        filename = f"spa_structure_{domain}.json"
        output_file = os.path.join(output_dir, filename)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(page_structure.model_dump(), f, ensure_ascii=False, indent=2)

        print(f"\n💾 详细结果已保存到: {output_file}")

    except Exception as e:
        print(f"❌ SPA页面抓取失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
