#!/usr/bin/env python3
"""
页面结构抓取器
基于 crawl4ai 库，抓取网页的结构信息
"""

import asyncio
import json
import os
import sys
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, urlparse
from pydantic import BaseModel, Field
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode


class PageStructure(BaseModel):
    """页面结构数据模型"""
    url: str = Field(..., description="页面URL")
    title: str = Field(default="", description="页面标题")
    description: str = Field(default="", description="页面描述")
    headings: List[Dict[str, str]] = Field(default_factory=list, description="标题列表")
    links: List[Dict[str, str]] = Field(default_factory=list, description="链接列表")
    images: List[Dict[str, str]] = Field(default_factory=list, description="图片列表")
    text_content: str = Field(default="", description="主要文本内容")
    meta_info: Dict[str, Any] = Field(default_factory=dict, description="元信息")


class PageStructureCrawler:
    """页面结构抓取器类"""

    def __init__(self, headless: bool = True, timeout: int = 30000, spa_mode: bool = False, wait_for_selector: str = None):
        """
        初始化抓取器

        Args:
            headless: 是否使用无头浏览器
            timeout: 页面加载超时时间（毫秒）
            spa_mode: 是否启用SPA模式（等待JavaScript渲染）
            wait_for_selector: 等待特定CSS选择器出现（用于SPA页面）
        """
        self.browser_config = BrowserConfig(
            headless=headless,
            java_script_enabled=True
        )
        self.timeout = timeout
        self.spa_mode = spa_mode
        self.wait_for_selector = wait_for_selector

    async def crawl_page_structure(self, url: str) -> PageStructure:
        """
        抓取页面结构

        Args:
            url: 要抓取的网页URL

        Returns:
            PageStructure: 页面结构信息
        """
        # 不再使用 CSS 选择器策略，改用 BeautifulSoup 直接解析

        # 配置抓取参数，针对SPA页面进行优化
        if self.spa_mode:
            # SPA模式：等待网络空闲，延长等待时间
            crawler_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=self.timeout,
                word_count_threshold=1,
                wait_until="networkidle",
                delay_before_return_html=3000,  # SPA模式下等待3秒让JS执行
                wait_for=self.wait_for_selector if self.wait_for_selector else None,
                js_code=["console.log('等待SPA渲染...'); await new Promise(resolve => setTimeout(resolve, 2000)); console.log('SPA渲染完成');"]  # 额外等待2秒
            )
        else:
            # 普通模式：快速加载
            crawler_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=self.timeout,
                word_count_threshold=1,
                wait_until="domcontentloaded",
                delay_before_return_html=0
            )

        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            # 获取基本页面内容
            result = await crawler.arun(url=url, config=crawler_config)

            if not result.success:
                raise Exception(f"抓取失败: {result.error_message}")

            # 使用 BeautifulSoup 直接解析 HTML
            headings = []
            links = []
            images = []

            if result.html:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(result.html, 'html.parser')

                # 提取标题
                for heading_tag in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                    text = heading_tag.get_text().strip()
                    if text:
                        headings.append({
                            'level': heading_tag.name,
                            'text': text
                        })

                # 提取链接
                for link_tag in soup.find_all('a', href=True):
                    href = link_tag.get('href', '').strip()
                    text = link_tag.get_text().strip()
                    if href and text:
                        # 转换相对链接为绝对链接
                        if not url.startswith('raw://'):
                            absolute_href = urljoin(url, href)
                        else:
                            absolute_href = href
                        links.append({
                            'text': text,
                            'href': absolute_href,
                            'title': link_tag.get('title', '').strip()
                        })

                # 提取图片
                for img_tag in soup.find_all('img', src=True):
                    src = img_tag.get('src', '').strip()
                    if src:
                        # 转换相对链接为绝对链接
                        if not url.startswith('raw://'):
                            absolute_src = urljoin(url, src)
                        else:
                            absolute_src = src
                        images.append({
                            'src': absolute_src,
                            'alt': img_tag.get('alt', '').strip(),
                            'title': img_tag.get('title', '').strip(),
                            'width': img_tag.get('width', ''),
                            'height': img_tag.get('height', '')
                        })

            # 提取页面标题和描述
            title = ""
            description = ""

            if result.html:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(result.html, 'html.parser')

                # 提取标题
                title_tag = soup.find('title')
                if title_tag:
                    title = title_tag.get_text().strip()

                # 提取描述
                desc_tag = soup.find('meta', attrs={'name': 'description'})
                if desc_tag:
                    description = desc_tag.get('content', '').strip()

            # 构建页面结构对象
            page_structure = PageStructure(
                url=url,
                title=title,
                description=description,
                headings=headings,
                links=links,
                images=images,
                text_content=result.markdown.raw_markdown if result.markdown else "",
                meta_info={
                    'status_code': getattr(result, 'status_code', None),
                    'content_length': len(result.html) if result.html else 0,
                    'markdown_length': len(result.markdown.raw_markdown) if result.markdown else 0,
                    'headings_count': len(headings),
                    'links_count': len(links),
                    'images_count': len(images)
                }
            )

            return page_structure


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python page_structure_crawler.py <URL或本地文件路径> [--spa]")
        print("示例: python page_structure_crawler.py https://example.com")
        print("示例: python page_structure_crawler.py test.html")
        print("示例: python page_structure_crawler.py http://localhost:7788 --spa")
        sys.exit(1)

    input_arg = sys.argv[1]
    spa_mode = '--spa' in sys.argv

    # 检查是否是本地文件
    if input_arg.endswith('.html') or input_arg.endswith('.htm'):
        # 本地HTML文件
        if not os.path.exists(input_arg):
            print(f"错误: 文件不存在: {input_arg}")
            sys.exit(1)

        # 读取HTML文件内容
        with open(input_arg, 'r', encoding='utf-8') as f:
            html_content = f.read()

        url = f"raw://{html_content}"
        print(f"正在抓取本地HTML文件: {input_arg}")
    else:
        # 网络URL
        url = input_arg
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            print("错误: 请提供有效的URL（包含http://或https://）或本地HTML文件路径")
            sys.exit(1)
        print(f"正在抓取页面结构: {url}")

    try:
        if spa_mode:
            print("🔄 启用SPA模式，将等待JavaScript渲染完成...")
            crawler = PageStructureCrawler(headless=True, timeout=60000, spa_mode=True)
        else:
            crawler = PageStructureCrawler(headless=True, timeout=30000, spa_mode=False)

        page_structure = await crawler.crawl_page_structure(url)

        # 输出结果
        print("\n" + "="*80)
        print("页面结构抓取结果")
        print("="*80)

        print(f"\n📄 页面信息:")
        print(f"  标题: {page_structure.title}")
        print(f"  URL: {page_structure.url}")
        print(f"  描述: {page_structure.description}")

        print(f"\n📊 统计信息:")
        for key, value in page_structure.meta_info.items():
            print(f"  {key}: {value}")

        if page_structure.headings:
            print(f"\n📋 页面标题 ({len(page_structure.headings)}个):")
            for i, heading in enumerate(page_structure.headings[:10], 1):  # 只显示前10个
                print(f"  {i}. [{heading['level']}] {heading['text']}")
            if len(page_structure.headings) > 10:
                print(f"  ... 还有 {len(page_structure.headings) - 10} 个标题")

        if page_structure.links:
            print(f"\n🔗 页面链接 ({len(page_structure.links)}个):")
            for i, link in enumerate(page_structure.links[:10], 1):  # 只显示前10个
                print(f"  {i}. {link['text']} -> {link['href']}")
            if len(page_structure.links) > 10:
                print(f"  ... 还有 {len(page_structure.links) - 10} 个链接")

        if page_structure.images:
            print(f"\n🖼️ 页面图片 ({len(page_structure.images)}个):")
            for i, img in enumerate(page_structure.images[:10], 1):  # 只显示前10个
                print(f"  {i}. {img['alt'] or '无描述'} -> {img['src']}")
            if len(page_structure.images) > 10:
                print(f"  ... 还有 {len(page_structure.images) - 10} 个图片")

        # 创建输出文件夹
        output_dir = "crawl_results"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 生成文件名和完整路径
        if input_arg.endswith('.html') or input_arg.endswith('.htm'):
            # 本地文件
            base_name = os.path.splitext(os.path.basename(input_arg))[0]
            filename = f"page_structure_{base_name}.json"
        else:
            # 网络URL
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.replace('.', '_').replace(':', '_')
            filename = f"page_structure_{domain}.json"

        output_file = os.path.join(output_dir, filename)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(page_structure.model_dump(), f, ensure_ascii=False, indent=2)

        print(f"\n💾 详细结果已保存到: {output_file}")

    except Exception as e:
        print(f"❌ 抓取失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
