<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="这是一个测试页面，用于验证页面结构抓取器的功能">
    <title>测试页面 - 页面结构抓取器</title>
</head>
<body>
    <header>
        <h1>页面结构抓取器测试</h1>
        <nav>
            <ul>
                <li><a href="#section1" title="第一部分">第一部分</a></li>
                <li><a href="#section2" title="第二部分">第二部分</a></li>
                <li><a href="https://example.com" title="外部链接">外部链接</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="section1">
            <h2>第一部分标题</h2>
            <p>这是第一部分的内容。包含一些文本和图片。</p>
            <img src="https://via.placeholder.com/300x200" alt="测试图片1" title="这是第一张测试图片" width="300" height="200">
            
            <h3>子标题 1.1</h3>
            <p>这里有一个链接：<a href="https://github.com" title="GitHub">GitHub</a></p>
        </section>

        <section id="section2">
            <h2>第二部分标题</h2>
            <p>这是第二部分的内容。</p>
            
            <h3>子标题 2.1</h3>
            <ul>
                <li><a href="/page1.html">内部页面1</a></li>
                <li><a href="/page2.html">内部页面2</a></li>
                <li><a href="mailto:<EMAIL>">邮件链接</a></li>
            </ul>
            
            <h4>子标题 2.1.1</h4>
            <p>更多内容和图片：</p>
            <img src="https://via.placeholder.com/400x300" alt="测试图片2" title="这是第二张测试图片">
            <img src="relative-image.jpg" alt="相对路径图片" width="150" height="100">
        </section>
    </main>

    <footer>
        <h5>页脚标题</h5>
        <p>版权信息和联系方式</p>
        <a href="https://crawl4ai.com" title="Crawl4AI官网">Crawl4AI</a>
    </footer>
</body>
</html>
